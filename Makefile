help:
	cat Makefile
serve:
	docker compose -f docker-compose.override.yml up -d
	head .env
	docker compose -f docker-compose.override.yml ps

clean:
	docker compose exec laravel php artisan config:clear
	docker compose exec laravel php artisan view:clear
	docker compose exec laravel php artisan route:clear
	docker compose exec laravel php artisan cache:clear

migrate:
	# docker-compose exec -it laravel php artisan migrate
	docker compose -f docker-compose.override.yml exec laravel php artisan migrate

seed:
	# docker-compose exec -it laravel php artisan db:seed
	docker compose exec laravel php artisan db:seed

stop:
	docker compose -f docker-compose.override.yml down
	docker compose -f docker-compose.override.yml ps
exec:
	docker compose exec -it laravel /bin/sh
	
logs:
	docker compose logs -f laravel

ps:
	docker compose -f docker-compose.override.yml ps

stop-prod:
	docker compose -f docker-compose.prod.yml down -v

build-prod:
	docker compose -f docker-compose.prod.yml build app

start-prod:
	docker compose -f docker-compose.prod.yml up -d

migrate-prod:
	docker compose -f docker-compose.prod.yml exec app php /var/www/html/artisan migrate --force

optimize-prod:
	docker compose -f docker-compose.prod.yml exec app php /var/www/html/artisan config:cache
	docker compose -f docker-compose.prod.yml exec app php /var/www/html/artisan route:cache
	docker compose -f docker-compose.prod.yml exec app php /var/www/html/artisan view:cache
	docker compose -f docker-compose.prod.yml exec app php /var/www/html/artisan event:cache

# Clear all caches (use when making changes to config, routes, or views)
clear-cache-prod:
	docker compose -f docker-compose.prod.yml exec app php /var/www/html/artisan config:clear
	docker compose -f docker-compose.prod.yml exec app php /var/www/html/artisan route:clear
	docker compose -f docker-compose.prod.yml exec app php /var/www/html/artisan view:clear
	docker compose -f docker-compose.prod.yml exec app php /var/www/html/artisan cache:clear
	docker compose -f docker-compose.prod.yml exec app php /var/www/html/artisan event:clear

rm-prod:
	docker rmi flowfront:prod
