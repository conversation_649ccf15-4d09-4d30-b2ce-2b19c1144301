#!/bin/bash
echo "=== Starting deployment ==="

echo "=== Checking initial build directory ==="
ls -la public/build || echo "Build directory doesn't exist yet"

echo "=== Cleaning npm cache and dependencies ==="
rm -rf node_modules package-lock.json
npm cache clean --force

echo "=== Installing NPM dependencies ==="
npm install
if [ $? -ne 0 ]; then
    echo "NPM install failed!"
    exit 1
fi

echo "=== Building assets ==="
npm run build
if [ $? -ne 0 ]; then
    echo "NPM build failed!"
    exit 1
fi

echo "=== Checking build directory after build ==="
ls -la public/build
echo "=== Checking manifest file specifically ==="
ls -la public/build/manifest.json || echo "Manifest file not found!"

echo "=== Copying environment file ==="
cp ".env.${ENV_CODE}" .env

echo "=== Clearing Laravel cache ==="
php artisan optimize:clear

# Cache the various components of the Laravel application
echo "=== Caching Laravel components ==="
php artisan config:cache
php artisan event:cache
php artisan route:cache
php artisan view:cache

echo "=== Verifying build files after cache operations ==="
ls -la public/build
ls -la public/build/manifest.json || echo "WARNING: Manifest file missing after cache operations!"

# Run any database migrations
echo "=== Running database migrations ==="
php artisan migrate --force

echo "=== Final verification of build files ==="
ls -la public/build
ls -la public/build/manifest.json || echo "ERROR: Manifest file missing at end of deployment!"

echo "=== Deployment completed ==="