
services:
  
  laravel:
    image: local-laravel:8.3
    volumes:
      - .:/work
      - ./docker/php/dev.ini:/usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini
    working_dir: /work
    ports:
      - "8154:8054"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    command: php artisan serve --host 0.0.0.0 --port 8054
    environment:
      - PHP_IDE_CONFIG=serverName=laravel
      - XDEBUG_MODE=develop,debug
    # Enable interactive mode
    tty: true
    stdin_open: true
    depends_on:
      - pg


  pg:
    image: pgvector/pgvector:pg17
    ports:
      - "15432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=secret
    volumes:
      - ./docker/pg/init.sql:/docker-entrypoint-initdb.d/01-init.sql
      # - ./database/snapshot/postgres-flowfront.sql:/docker-entrypoint-initdb.d/02-flowfront.sql
      - pg_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD", "pg_isready", "-U", "postgres"]
      interval: 10s
      timeout: 5s
      retries: 5


volumes:
  mariadb_data:
  pg_data: