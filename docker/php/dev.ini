; [xdebug]
; xdebug.remote_enable=1
; xdebug.remote_connect_back=0
; xdebug.remote_port=9103
; xdebug.remote_host=host.docker.internal
; ; xdebug.remote_host=docker.host.internal
; xdebug.idekey=vsc
; xdebug.remote_handler=dbgp


; ; docker image default config
; zend_extension=xdebug
; xdebug.mode=develop,debug
; xdebug.client_host=host.docker.internal
; xdebug.start_with_request=yes
; xdebug.log=/var/log/xdebug.log


; phpstome mp
; [xdebug]
; zend_extension=xdebug.so
; xdebug.mode=debug
; xdebug.discover_client_host=false
; xdebug.client_port=9103
; xdebug.client_host=host.docker.internal
; xdebug.remote_handler=dbgp
; xdebug.start_with_request=yes
; xdebug.remote_mode=req
; xdebug.log=/var/log/php-xdebug_remote.log
; xdebug.idekey=vsc

; https://gist.github.com/megahirt/e80086d1d029a7406e9eaec1fb1dcc9e
[xdebug]
zend_extension=xdebug
xdebug.remote_port=9103
xdebug.client_port=9103
xdebug.mode=off
xdebug.start_with_request = yes
xdebug.client_host = "host.docker.internal"
xdebug.idekey="vsc"
xdebug.log=/tmp/xdebug_remote.log