<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cards', function (Blueprint $table) {
            $table->unsignedBigInteger('id')->primary();
            $table->text('url')->comment('URL of the model or dataset');
            $table->string('title')->comment('Model name or dataset name');
            $table->enum('type', ['model', 'dataset'])->comment('Type of card: model or dataset');
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->nullable()->comment('Record last updated timestamp');
            $table->timestamp('last_renew_at')->nullable()->comment('When web page information was last updated from crawl');
            $table->timestamp('last_edited_at')->nullable()->comment('When card was last manually edited by user from web UI');

            // Indexes for performance
            $table->index(['type', 'created_at']);
            $table->index('last_renew_at');
            $table->index('last_edited_at');
            $table->index('url'); // For URL lookups
        });

        // Create trigger function for setting ID
        DB::unprepared('
            CREATE OR REPLACE FUNCTION set_card_id()
            RETURNS TRIGGER AS $$
            BEGIN
              IF NEW.id IS NULL THEN
                NEW.id := generate_ms_id();
              END IF;
              RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        ');

        // Install trigger for setting ID on insert
        DB::unprepared('
            CREATE TRIGGER trg_set_card_id
            BEFORE INSERT ON cards
            FOR EACH ROW
            EXECUTE FUNCTION set_card_id();
        ');

        // Install trigger for updating updated_at
        DB::unprepared('
            CREATE TRIGGER trg_update_card_updated_at
            BEFORE UPDATE ON cards
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop triggers first
        DB::unprepared('DROP TRIGGER IF EXISTS trg_set_card_id ON cards');
        DB::unprepared('DROP TRIGGER IF EXISTS trg_update_card_updated_at ON cards');
        // Then drop the function
        DB::unprepared('DROP FUNCTION IF EXISTS set_card_id()');
        // Finally drop the table
        Schema::dropIfExists('cards');
    }
};
