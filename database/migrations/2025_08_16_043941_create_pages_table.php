<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->unsignedBigInteger('id')->primary();
            $table->string('title')->nullable();
            $table->string('url');
            $table->string('page_type')->nullable(false);
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('updated_at')->nullable();
            $table->timestamp('last_crawled_at')->nullable();
            $table->integer('last_crawled_code')->nullable();
            $table->boolean('is_activated')->default(true);
        });

        // Create trigger function for setting ID
        DB::unprepared('
            CREATE OR REPLACE FUNCTION set_page_id()
            RETURNS TRIGGER AS $$
            BEGIN
              IF NEW.id IS NULL THEN
                NEW.id := generate_ms_id();
              END IF;
              RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        ');

        // Install trigger for setting ID on insert
        DB::unprepared('
            CREATE TRIGGER trg_set_page_id
            BEFORE INSERT ON pages
            FOR EACH ROW
            EXECUTE FUNCTION set_page_id();
        ');

        // Install trigger for updating updated_at
        DB::unprepared('
            CREATE TRIGGER trg_update_updated_at
            BEFORE UPDATE ON pages
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop triggers first
        DB::unprepared('DROP TRIGGER IF EXISTS trg_set_page_id ON pages');
        DB::unprepared('DROP TRIGGER IF EXISTS trg_update_updated_at ON pages');
        // Then drop the functions
        DB::unprepared('DROP FUNCTION IF EXISTS set_page_id()');
        DB::unprepared('DROP FUNCTION IF EXISTS update_updated_at_column()');
        // Finally drop the table
        Schema::dropIfExists('pages');
    }
};
