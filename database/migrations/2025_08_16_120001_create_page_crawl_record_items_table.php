<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('page_crawl_record_items', function (Blueprint $table) {
            $table->unsignedBigInteger('id')->primary();
            $table->unsignedBigInteger('page_id')->comment('Reference to the page this item belongs to');
            $table->unsignedBigInteger('record_id')->comment('Reference to the crawl record this item was extracted from');
            $table->text('link')->comment('URL or link associated with this crawl item');
            $table->text('text')->comment('Text content extracted from the crawled page');
            $table->timestamp('created_at')->useCurrent();
            $table->timestamp('processed_at')->nullable()->comment('When this item was processed for card extraction');

            // Foreign key constraints
            $table->foreign('page_id')->references('id')->on('pages')->onDelete('cascade');
            $table->foreign('record_id')->references('id')->on('page_crawl_records')->onDelete('cascade');

            // Indexes for performance
            $table->index(['page_id', 'created_at']);
            $table->index(['record_id', 'created_at']);
            $table->index('processed_at');
        });

        // Create trigger function for setting ID
        DB::unprepared('
            CREATE OR REPLACE FUNCTION set_page_crawl_record_item_id()
            RETURNS TRIGGER AS $$
            BEGIN
              IF NEW.id IS NULL THEN
                NEW.id := generate_ms_id();
              END IF;
              RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        ');

        // Install trigger for setting ID on insert
        DB::unprepared('
            CREATE TRIGGER trg_set_page_crawl_record_item_id
            BEFORE INSERT ON page_crawl_record_items
            FOR EACH ROW
            EXECUTE FUNCTION set_page_crawl_record_item_id();
        ');

        // Install trigger for updating processed_at when needed
        DB::unprepared('
            CREATE TRIGGER trg_update_page_crawl_record_item_processed_at
            BEFORE UPDATE ON page_crawl_record_items
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop triggers first
        DB::unprepared('DROP TRIGGER IF EXISTS trg_set_page_crawl_record_item_id ON page_crawl_record_items');
        DB::unprepared('DROP TRIGGER IF EXISTS trg_update_page_crawl_record_item_processed_at ON page_crawl_record_items');
        // Then drop the function
        DB::unprepared('DROP FUNCTION IF EXISTS set_page_crawl_record_item_id()');
        // Finally drop the table
        Schema::dropIfExists('page_crawl_record_items');
    }
};
