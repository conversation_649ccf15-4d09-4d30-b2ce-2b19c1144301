<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        DB::unprepared('
            CREATE OR REPLACE FUNCTION generate_ms_id()
            RETURNS BIGINT AS $$
            BEGIN
                RETURN floor(EXTRACT(EPOCH FROM clock_timestamp()) * 1000)::BIGINT * 1000
                       + floor(random() * 1000)::INT;
            END;
            $$ LANGUAGE plpgsql;
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::unprepared('DROP FUNCTION IF EXISTS generate_ms_id()');
    }
};
