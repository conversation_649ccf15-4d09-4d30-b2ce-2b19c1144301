<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('page_crawl_records', function (Blueprint $table) {
            $table->unsignedBigInteger('id')->primary();
            $table->unsignedBigInteger('page_id');
            $table->integer('status_code')->nullable();
            $table->string('status_message')->nullable();
            $table->string('storage_path')->nullable()->comment('Path to JSON file containing crawled content');
            $table->timestamp('created_at')->useCurrent();
            $table->boolean('are_cards_processed')->default(false)->comment('Whether cards have been extracted from this crawl');
            $table->timestamp('last_processed_at')->nullable()->comment('When cards were last extracted from this crawl');
            $table->integer('total_cards')->nullable()->comment('Number of cards found during processing');

            // Foreign key constraint
            $table->foreign('page_id')->references('id')->on('pages')->onDelete('cascade');

            // Index for performance
            $table->index(['page_id', 'created_at']);
        });

        // Create trigger function for setting ID
        DB::unprepared('
            CREATE OR REPLACE FUNCTION set_page_crawl_record_id()
            RETURNS TRIGGER AS $$
            BEGIN
              IF NEW.id IS NULL THEN
                NEW.id := generate_ms_id();
              END IF;
              RETURN NEW;
            END;
            $$ LANGUAGE plpgsql;
        ');

        // Install trigger for setting ID on insert
        DB::unprepared('
            CREATE TRIGGER trg_set_page_crawl_record_id
            BEFORE INSERT ON page_crawl_records
            FOR EACH ROW
            EXECUTE FUNCTION set_page_crawl_record_id();
        ');
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop trigger first
        DB::unprepared('DROP TRIGGER IF EXISTS trg_set_page_crawl_record_id ON page_crawl_records');
        // Then drop the function
        DB::unprepared('DROP FUNCTION IF EXISTS set_page_crawl_record_id()');
        // Finally drop the table
        Schema::dropIfExists('page_crawl_records');
    }
};
