drop database railway;

-- 定义变量（在 psql 执行时通过 -v 传入或提前 \set）
-- 用户名、密码、schema
-- \set user_name      'carduser'
-- \set user_password  'carduser'
-- \set schema_name    'cardbucket'

-- 创建用户 :user_name，密码 :user_password，权限受限
CREATE USER carduser WITH
    LOGIN
    PASSWORD 'card9xc0o23sd902'
    NOSUPERUSER
    NOCREATEDB
    NOCREATEROLE
    NOINHERIT
    NOREPLICATION
    NOBYPASSRLS;

-- 创建 schema :schema_name，并将 carduser 设为所有者
CREATE SCHEMA cardbucket AUTHORIZATION carduser;

-- 显式授权 carduser 使用和创建 :schema_name schema 中对象的权限
GRANT USAGE, CREATE ON SCHEMA cardbucket TO carduser;

-- 设置默认 schema
ALTER ROLE carduser SET search_path TO cardbucket;

-- 撤销 public 权限
REVOKE ALL ON SCHEMA public FROM carduser;
REVOKE ALL ON SCHEMA public FROM PUBLIC;

-- 限制访问 pg_catalog 和 information_schema（可选但有风险）
REVOKE USAGE ON SCHEMA pg_catalog FROM carduser;
REVOKE USAGE ON SCHEMA information_schema FROM carduser;

