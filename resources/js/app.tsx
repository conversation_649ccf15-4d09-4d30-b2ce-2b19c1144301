import React from 'react';
import { createRoot } from 'react-dom/client';
import Dashboard from './components/Dashboard';

// Mount the Dashboard component if the dashboard container exists
const dashboardContainer = document.getElementById('dashboard-root');
if (dashboardContainer) {
    const root = createRoot(dashboardContainer);
    root.render(<Dashboard />);
}

// You can add other React components here for different pages
// For example:
// const homeContainer = document.getElementById('home-root');
// if (homeContainer) {
//     const root = createRoot(homeContainer);
//     root.render(<HomePage />);
// }
